"use client";
import React from 'react';
import { motion } from 'framer-motion';

/**
 * QuickLinks Component
 *
 * Bottom navigation section with blog link buttons.
 * Positioned at the bottom of the orange portfolio container.
 *
 * Design Features:
 * - 4-column grid layout as specified in Figma
 * - Pink background buttons (#fadcd9) with black borders
 * - Hover effects that invert colors (black background, white text)
 * - Arrow icons with micro-interactions
 * - Accessibility support with ARIA labels and focus states
 * - Staggered entrance animations
 */
const QuickLinks = () => {
  // Blog link data - 4 items for the grid layout
  const blogLinks = [
    { id: 1, title: 'BLOG', url: '/blog/1' },
    { id: 2, title: 'BLOG', url: '/blog/2' },
    { id: 3, title: 'BLOG', url: '/blog/3' },
    { id: 4, title: 'BLOG', url: '/blog/4' }
  ];

  return (
    {/*
      Main Container
      Full width to span the bottom of the orange container
      Delayed entrance animation to appear after other elements
    */}
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 1.2 }}
      viewport={{ once: true }}
    >
      {/*
        Section Title
        Large, bold heading positioned above the button grid
        Black text for contrast against orange background
        Left-aligned as shown in Figma design
      */}
      <motion.h3
        className="text-black font-['Gilroy:Bold',_sans-serif] text-[36px] font-bold mb-6 tracking-wide"
        initial={{ opacity: 0, x: -20 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
        viewport={{ once: true }}
      >
        QUICK LINKS
      </motion.h3>

      {/*
        Blog Buttons Grid
        4-column layout matching Figma design
        Each button has hover effects and animations
      */}
      <motion.div
        className="grid grid-cols-4 gap-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.6 }}
        viewport={{ once: true }}
      >
        {/*
          Individual Blog Buttons
          Each button represents a blog link with:
          - Pink background (#fadcd9) matching social links
          - Black border for definition
          - Hover state inverts colors (black bg, white text)
          - Arrow icon with micro-interaction
          - Accessibility features (ARIA labels, focus states)
        */}
        {blogLinks.map((link, index) => (
          <motion.a
            key={link.id}
            href={link.url}
            aria-label={`Visit ${link.title} ${link.id}`}
            className="group relative bg-[#fadcd9] border-2 border-black rounded-lg px-6 py-4 flex items-center justify-between hover:bg-black hover:text-white transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.9 + index * 0.1 }}
            viewport={{ once: true }}
          >
            {/*
              Button Text
              Bold typography matching the design system
              Color transitions with hover state
            */}
            <span className="text-black font-['Gilroy:Bold',_sans-serif] text-[18px] font-bold group-hover:text-white transition-colors duration-300">
              {link.title}
            </span>

            {/*
              Arrow Icon
              Diagonal arrow pointing up-right
              Subtle movement on hover for micro-interaction
              Color transitions with button hover state
            */}
            <motion.svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-black group-hover:text-white transition-colors duration-300"
              whileHover={{ x: 3 }}
              transition={{ duration: 0.2 }}
            >
              <path
                d="M7 17L17 7M17 7H7M17 7V17"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </motion.svg>
          </motion.a>
        ))}
      </motion.div>
    </motion.div>
  );
};

export default QuickLinks;
