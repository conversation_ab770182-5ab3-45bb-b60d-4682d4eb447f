"use client";
import React from 'react';
import { motion } from 'framer-motion';

const QuickLinks = () => {
  const blogLinks = [
    { id: 1, title: 'BLOG', url: '/blog/1' },
    { id: 2, title: '<PERSON><PERSON><PERSON><PERSON>', url: '/blog/2' },
    { id: 3, title: '<PERSON><PERSON>O<PERSON>', url: '/blog/3' },
    { id: 4, title: 'BLOG', url: '/blog/4' }
  ];

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
      viewport={{ once: true }}
    >
      {/* Quick Links Title */}
      <motion.h3
        className="text-black font-['Gilroy:Bold',_sans-serif] text-[24px] md:text-[32px] font-bold mb-4 md:mb-6"
        initial={{ opacity: 0, x: -20 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.7 }}
        viewport={{ once: true }}
      >
        QUICK LINKS
      </motion.h3>

      {/* Blog Buttons Grid */}
      <motion.div
        className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        viewport={{ once: true }}
      >
        {blogLinks.map((link, index) => (
          <motion.a
            key={link.id}
            href={link.url}
            aria-label={`Visit ${link.title} ${link.id}`}
            className="group relative bg-[#fadcd9] border-2 border-black rounded-lg px-4 md:px-6 py-3 md:py-4 flex items-center justify-between hover:bg-black hover:text-white transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.9 + index * 0.1 }}
            viewport={{ once: true }}
          >
            {/* Blog Text */}
            <span className="text-black font-['Gilroy:Bold',_sans-serif] text-[16px] md:text-[18px] font-bold group-hover:text-white transition-colors duration-300">
              {link.title}
            </span>

            {/* Arrow Icon */}
            <motion.svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-black group-hover:text-white transition-colors duration-300"
              whileHover={{ x: 3 }}
              transition={{ duration: 0.2 }}
            >
              <path
                d="M7 17L17 7M17 7H7M17 7V17"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </motion.svg>
          </motion.a>
        ))}
      </motion.div>
    </motion.div>
  );
};

export default QuickLinks;
