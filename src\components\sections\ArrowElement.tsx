"use client";
import React from 'react';
import { motion } from 'framer-motion';

const ArrowElement = () => {
  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, rotate: -10 }}
      whileInView={{ opacity: 1, rotate: 0 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      viewport={{ once: true }}
    >
      {/* Arrow SVG */}
      <motion.svg
        width="120"
        height="120"
        viewBox="0 0 120 120"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="transform rotate-45"
        whileHover={{ scale: 1.1, rotate: 50 }}
        transition={{ duration: 0.3 }}
      >
        {/* Arrow shaft */}
        <motion.path
          d="M20 60 L85 60"
          stroke="#000000"
          strokeWidth="8"
          strokeLinecap="round"
          initial={{ pathLength: 0 }}
          whileInView={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          viewport={{ once: true }}
        />
        
        {/* Arrow head */}
        <motion.path
          d="M70 45 L85 60 L70 75"
          stroke="#000000"
          strokeWidth="8"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
          initial={{ pathLength: 0 }}
          whileInView={{ pathLength: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        />
      </motion.svg>

      {/* Decorative lines */}
      <motion.div
        className="absolute -bottom-8 -left-4 w-16 h-0.5 bg-black opacity-60"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 0.6, delay: 1 }}
        viewport={{ once: true }}
      />
      <motion.div
        className="absolute -bottom-12 -left-8 w-24 h-0.5 bg-black opacity-40"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 0.6, delay: 1.2 }}
        viewport={{ once: true }}
      />
    </motion.div>
  );
};

export default ArrowElement;
