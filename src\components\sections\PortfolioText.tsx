"use client";
import React from 'react';
import { motion } from 'framer-motion';

const PortfolioText = () => {
  return (
    <motion.div
      className="text-center md:text-right max-w-[400px] mx-auto md:mx-0"
      initial={{ opacity: 0, x: 50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
      viewport={{ once: true }}
    >
      {/* Description Text */}
      <motion.p
        className="text-white font-['Gilroy:Light',_sans-serif] text-[14px] md:text-[16px] leading-[20px] md:leading-[22px] mb-4 md:mb-6"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        viewport={{ once: true }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </motion.p>

      {/* Portfolio Title */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, scale: 0.9 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.7 }}
        viewport={{ once: true }}
      >
        {/* Year */}
        <motion.span
          className="text-white font-['Gilroy:Bold',_sans-serif] text-[32px] md:text-[48px] leading-none block"
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
        >
          2025
        </motion.span>
        
        {/* Portfolio */}
        <motion.span
          className="text-white font-['Gilroy:Bold',_sans-serif] text-[48px] md:text-[72px] leading-none block font-bold tracking-wider"
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          viewport={{ once: true }}
        >
          PORTFOLIO
        </motion.span>
      </motion.div>
    </motion.div>
  );
};

export default PortfolioText;
