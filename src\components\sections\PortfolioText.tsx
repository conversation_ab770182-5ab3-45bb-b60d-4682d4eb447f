"use client";
import React from 'react';
import { motion } from 'framer-motion';

/**
 * PortfolioText Component
 *
 * Main content area displaying the portfolio title and description.
 * Positioned on the right side within the orange container.
 *
 * Design Features:
 * - Right-aligned text layout matching Figma design
 * - Large typography hierarchy (description → year → PORTFOLIO)
 * - White text on orange background for high contrast
 * - Staggered animations for visual appeal
 * - Responsive font sizes for different screen sizes
 */
const PortfolioText = () => {
  return (
    {/*
      Main Text Container
      Right-aligned layout with controlled max-width
      Slides in from right with opacity fade
    */}
    <motion.div
      className="text-right max-w-[450px]"
      initial={{ opacity: 0, x: 50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: 0.6 }}
      viewport={{ once: true }}
    >
      {/*
        Description Text
        Positioned above the main title as shown in Figma design
        Uses Gilroy Light font for readability
        Right-aligned to match overall text alignment
      */}
      <motion.p
        className="text-white font-['<PERSON>roy:Light',_sans-serif] text-[16px] leading-[24px] mb-8 text-right"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        viewport={{ once: true }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </motion.p>

      {/*
        Portfolio Title Section
        Two-part title with year and "PORTFOLIO" text
        Large typography creates strong visual hierarchy
        Staggered animation for dramatic entrance effect
      */}
      <motion.div
        className="relative text-right"
        initial={{ opacity: 0, scale: 0.9 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 1 }}
        viewport={{ once: true }}
      >
        {/*
          Year Display - "2025"
          Smaller than main title but still prominent
          Positioned above PORTFOLIO as shown in Figma
          Uses Gilroy Bold for strong visual weight
        */}
        <motion.div
          className="text-white font-['Gilroy:Bold',_sans-serif] text-[56px] leading-none block mb-2"
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          viewport={{ once: true }}
        >
          2025
        </motion.div>

        {/*
          Main Title - "PORTFOLIO"
          Largest text element and main focal point
          Extra bold weight and wide letter spacing
          Creates strong visual impact as the hero element
        */}
        <motion.div
          className="text-white font-['Gilroy:Bold',_sans-serif] text-[84px] leading-none block font-bold tracking-wider"
          initial={{ opacity: 0, x: 40 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 1.4 }}
          viewport={{ once: true }}
        >
          PORTFOLIO
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default PortfolioText;
