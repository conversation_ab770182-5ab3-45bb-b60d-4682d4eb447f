"use client";
import React from 'react';
import { motion } from 'framer-motion';

/**
 * SocialMediaLinks Component
 *
 * Displays social media navigation links in a rounded container.
 * Positioned at the top of the orange portfolio container.
 *
 * Design Features:
 * - Pink/beige background (#fadcd9) matching Figma design
 * - Rounded pill shape container
 * - Hover effects with color transitions
 * - Accessibility support with ARIA labels and focus states
 * - Responsive spacing and typography
 */
const SocialMediaLinks = () => {
  // Social media platforms as specified in Figma design
  const socialLinks = [
    { name: 'INSTAGRAM', url: 'https://instagram.com' },
    { name: 'TWITTER', url: 'https://twitter.com' },
    { name: 'LINKEDIN', url: 'https://linkedin.com' }
  ];

  return (
    {/*
      Main Container - Pink rounded pill
      Background color: #fadcd9 (brand pink from Figma)
      Shape: Fully rounded (rounded-full)
      Layout: Horizontal flex with centered items
    */}
    <motion.div
      className="bg-[#fadcd9] rounded-full px-4 md:px-8 py-3 md:py-4 flex items-center gap-4 md:gap-8"
      initial={{ opacity: 0, y: -20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      viewport={{ once: true }}
    >
      {/*
        Social Media Links - Individual link buttons
        Each link has hover effects and accessibility features
        Staggered animation entrance for visual appeal
      */}
      {socialLinks.map((link, index) => (
        <motion.a
          key={link.name}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={`Visit our ${link.name} page`}
          className="text-[#000000] font-['Gilroy:Medium',_sans-serif] text-[12px] md:text-[14px] font-medium tracking-wide hover:text-[#feb273] transition-colors duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#feb273] focus:ring-opacity-50 rounded-sm px-1 py-1"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, x: -10 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
          viewport={{ once: true }}
        >
          {link.name}
        </motion.a>
      ))}
    </motion.div>
  );
};

export default SocialMediaLinks;
