"use client";
import React from 'react';
import { motion } from 'framer-motion';
import SocialMediaLinks from './SocialMediaLinks';
import ArrowElement from './ArrowElement';
import PortfolioText from './PortfolioText';
import QuickLinks from './QuickLinks';

/**
 * PortfolioSection Component
 *
 * Main portfolio showcase section that matches the exact Figma design layout.
 *
 * Layout Structure:
 * - Left side (30-40% width): White space area containing the arrow element
 * - Right side (60-70% width): Orange rounded container with portfolio content
 *
 * Design Features:
 * - Responsive layout that adapts to different screen sizes
 * - Smooth animations using Framer Motion
 * - Exact color matching from Figma design (#feb273 orange, #fadcd9 pink)
 * - Proper spacing and typography hierarchy
 */
const PortfolioSection = () => {
  return (
    <section id="portfolio" className="relative w-full min-h-screen bg-white overflow-hidden">
      {/*
        Main Layout Container
        Uses flexbox to create the two-column layout:
        - Left: White space with arrow (30-40% width)
        - Right: Orange container (60-70% width)
      */}
      <div className="relative w-full h-screen flex">

        {/*
          Left Side - White Space Area (30-40% width)
          Contains the arrow element that points toward the portfolio container.
          This matches the Figma design where the arrow is positioned in the left white space.
        */}
        <div className="relative w-[30%] md:w-[35%] lg:w-[40%] h-full flex items-center justify-center">
          {/*
            Arrow Element - Main focal point in left area
            Uses the provided Arrow 1.png image from public/img/
            Positioned to point toward the orange portfolio container
          */}
          <div className="relative z-10">
            <ArrowElement />
          </div>

          {/*
            Decorative Lines - Subtle design elements
            These horizontal lines extend from the arrow area and add visual interest
            Positioned in the lower portion of the left area as seen in Figma
          */}
          <div className="absolute bottom-[20%] left-[10%] right-[20%]">
            {/* Primary decorative line */}
            <motion.div
              className="w-full h-[2px] bg-black opacity-30 mb-4"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              viewport={{ once: true }}
            />
            {/* Secondary decorative line */}
            <motion.div
              className="w-[80%] h-[2px] bg-black opacity-20"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              transition={{ duration: 0.8, delay: 1.4 }}
              viewport={{ once: true }}
            />
          </div>
        </div>

        {/*
          Right Side - Orange Portfolio Container (60-70% width)
          Main content area that contains all portfolio information.
          Positioned on the right side with proper spacing from screen edge.
        */}
        <div className="relative w-[70%] md:w-[65%] lg:w-[60%] h-full flex items-center justify-end pr-8 md:pr-16">
          {/*
            Orange Container - Main portfolio showcase
            Color: #feb273 (brand orange from Figma design)
            Features: Rounded corners, shadow, hover effects
            Height: 85vh to maintain proper proportions
          */}
          <motion.div
            className="relative w-full max-w-[800px] h-[85vh] bg-[#feb273] rounded-[40px] md:rounded-[60px] overflow-hidden shadow-2xl"
            initial={{ opacity: 0, scale: 0.9, x: 100 }}
            whileInView={{ opacity: 1, scale: 1, x: 0 }}
            whileHover={{ scale: 1.01 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            {/*
              Social Media Links - Top Navigation
              Positioned at the top center of the orange container
              Contains Instagram, Twitter, and LinkedIn links in pink container
            */}
            <div className="absolute top-8 md:top-12 left-1/2 transform -translate-x-1/2 z-10">
              <SocialMediaLinks />
            </div>

            {/*
              Portfolio Text - Main Content
              Contains "2025 PORTFOLIO" title and description text
              Positioned on the right side within the orange container
              Right-aligned to match Figma design
            */}
            <div className="absolute right-8 md:right-12 top-1/2 transform -translate-y-1/2 z-10">
              <PortfolioText />
            </div>

            {/*
              Quick Links - Bottom Navigation
              Grid of blog buttons with arrows
              Spans the full width at the bottom of the container
              4-column layout as specified in Figma
            */}
            <div className="absolute bottom-8 md:bottom-12 left-8 md:left-12 right-8 md:right-12 z-10">
              <QuickLinks />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
