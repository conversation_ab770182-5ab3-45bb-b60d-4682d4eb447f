"use client";
import React from 'react';
import { motion } from 'framer-motion';
import SocialMediaLinks from './SocialMediaLinks';
import ArrowElement from './ArrowElement';
import PortfolioText from './PortfolioText';
import QuickLinks from './QuickLinks';

const PortfolioSection = () => {
  return (
    <section id="portfolio" className="relative w-full min-h-screen bg-white flex items-center justify-center py-8 md:py-16 px-4 md:px-6">
      {/* Main Portfolio Container */}
      <motion.div
        className="relative w-full max-w-[1200px] h-[600px] md:h-[800px] bg-[#feb273] rounded-[20px] md:rounded-[40px] overflow-hidden shadow-2xl"
        initial={{ opacity: 0, scale: 0.9 }}
        whileInView={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        {/* Social Media Links - Top */}
        <div className="absolute top-4 md:top-8 left-1/2 transform -translate-x-1/2 z-10">
          <SocialMediaLinks />
        </div>

        {/* Arrow Element - Left side (hidden on mobile) */}
        <div className="hidden md:block absolute left-8 lg:left-16 top-1/2 transform -translate-y-1/2 z-10">
          <ArrowElement />
        </div>

        {/* Portfolio Text - Right side / Center on mobile */}
        <div className="absolute right-4 md:right-8 lg:right-16 top-1/2 md:top-1/2 transform -translate-y-1/2 md:-translate-y-1/2 z-10 w-full md:w-auto px-4 md:px-0">
          <PortfolioText />
        </div>

        {/* Quick Links - Bottom */}
        <div className="absolute bottom-4 md:bottom-8 left-4 md:left-8 right-4 md:right-8 z-10">
          <QuickLinks />
        </div>
      </motion.div>
    </section>
  );
};

export default PortfolioSection;
